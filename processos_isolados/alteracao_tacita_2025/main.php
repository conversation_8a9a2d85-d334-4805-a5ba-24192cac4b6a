<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/helpers.php';

$contratosProRata = getContratosProRata();

main();

function main() {
    // $dadosContrato = getDadosContrato(129385);
    // var_dump($dadosContrato);
    // atualizarContrato(110741);
    // salvarDadosPre(844);
    salvarDadosPos(844);    
}

function atualizarContrato($id_contrato) {
    global $contratosProRata;
    $contrato = getContrato(110741);

    if (in_array($contrato['id'], $contratosProRata)) {
        
    }
}

// ----------------------------------------------------
// ----------------------------------------------------

function getContratosProRata() {
    global $dbh_ixc;

    $stmt = $dbh_ixc->prepare("SELECT DISTINCT id_contrato
        FROM cliente_contrato_servicos
        WHERE incluido_por_prorata = 'S';");
    $stmt->execute();

    $contratos = $stmt->fetchAll(PDO::FETCH_COLUMN);

    return $contratos;
}

function adicionarValorDesconto() {
    $api = getIxcApi();

    $dados = [
        'descricao' => 'Teste',
        'valor' => 10.50,
        'id_contrato' => 110741,
        'id_vd_contrato_produtos' => 390859,
    ];

    $api->post('cliente_contrato_descontos', $dados);

    $retorno = $api->getRespostaConteudo(true);

    var_dump($retorno);
}

function getNovoPlanoVenda($id_plano_venda_atual, $cidade) {
    global $dbh_noc;

    $dbh_noc->prepare("SELECT id_plano_venda_novo FROM temp.alteracao_tacita_2025 WHERE id_plano_venda_atual = ? AND cidade = ?;");
    $dbh_noc->execute([$id_plano_venda_atual, $cidade]);

    $novoPlanoVenda = $dbh_noc->fetch(PDO::FETCH_COLUMN);

    return $novoPlanoVenda;
}

function setAborted($id_contrato, $info) {

}

function salvarDadosPre($id_contrato) {
    return salvarDadosContrato($id_contrato, 'pre');
}

function salvarDadosPos($id_contrato) {
    return salvarDadosContrato($id_contrato, 'pos');
}