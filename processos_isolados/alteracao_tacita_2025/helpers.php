<?php
require_once __DIR__ . '/../../common/config.php';

$dbh_ixc = getConnectionIxc();
$dbh_noc = getConnectionNoc();

function getDadosContrato($id_contrato) {
    global $dbh_ixc;

    $stmt = $dbh_ixc->prepare("SELECT cc.id AS id_contrato,
        cc.id_vd_contrato AS plano_venda,
        ru.id_grupo AS plano_velocidade,
        cc.data_renovacao,
        cc.data_expiracao,
        SUM(vcp.valor_unit) AS valor_produtos,
        SUM(
            (CASE WHEN cca.valor > 0 THEN cca.valor ELSE 0 END)
        ) AS valor_acrescimos,
        SUM(
            (CASE WHEN ccd.valor > 0 THEN ccd.valor ELSE 0 END)
        ) AS valor_descontos,
        SUM(
            vcp.valor_unit
            + (CASE WHEN cca.valor > 0 THEN cca.valor ELSE 0 END)
            - (CASE WHEN ccd.valor > 0 THEN ccd.valor ELSE 0 END)
        ) AS valor_final,
        (CASE
            WHEN ru.endereco_padrao_cliente = 'S' THEN ci3.nome
            WHEN ru.endereco_padrao_cliente = 'N' AND ru.cidade != 0 AND ru.cidade IS NOT NULL THEN ci1.nome
            WHEN cc.endereco_padrao_cliente = 'N' AND cc.cidade != 0 AND cc.cidade IS NOT NULL THEN ci2.nome
            ELSE ci3.nome
        END) AS cidade,
        (
            SELECT JSON_ARRAYAGG(ar.id)
            FROM fn_areceber ar 
            WHERE ar.id_contrato = cc.id AND ar.status = 'A'
        ) AS ids_faturas_abertas
    FROM cliente_contrato cc
    LEFT JOIN cliente cl ON cl.id = cc.id_cliente
    LEFT JOIN radusuarios ru ON ru.id_contrato = cc.id
    LEFT JOIN vd_contratos_produtos vcp ON vcp.id_contrato = cc.id OR vcp.id_vd_contrato = cc.id_vd_contrato
    LEFT JOIN cliente_contrato_acrescimos cca ON cca.id_vd_contrato_produtos = vcp.id AND cca.id_contrato = cc.id AND (cca.data_validade = '0000-00-00' OR cca.data_validade IS NULL OR cca.data_validade > NOW())
    LEFT JOIN cliente_contrato_descontos ccd ON ccd.id_vd_contrato_produtos = vcp.id AND ccd.id_contrato = cc.id AND (ccd.data_validade = '0000-00-00' OR ccd.data_validade IS NULL OR ccd.data_validade > NOW())
    LEFT JOIN cidade ci1 ON ci1.id = ru.cidade
    LEFT JOIN cidade ci2 ON ci2.id = cc.cidade
    LEFT JOIN cidade ci3 ON ci3.id = cl.cidade
    WHERE cc.id = ?;");

    $stmt->execute([$id_contrato]);

    $dados = $stmt->fetch(PDO::FETCH_ASSOC);

    return $dados;
}

function salvarDadosContrato($id_contrato, $type) {
    global $dbh_noc;

    if (!in_array($type, ['pre', 'pos']))
        return false;

    $table = $type == 'pre' ? 'dados_pre' : 'dados_pos';

    $dadosContrato = getDadosContrato($id_contrato);

    $stmt = $dbh_noc->prepare("INSERT INTO alteracao_tacita_2025.$table (id_contrato, plano_venda, plano_velocidade, data_renovacao, data_expiracao, valor_produtos, valor_acrescimos, valor_descontos, valor_final, cidade, ids_faturas_abertas) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);");
    $query = $stmt->execute([
        $dadosContrato['id_contrato'],$dadosContrato['plano_venda'], $dadosContrato['plano_velocidade'], $dadosContrato['data_renovacao'], $dadosContrato['data_expiracao'], $dadosContrato['valor_produtos'], $dadosContrato['valor_acrescimos'], $dadosContrato['valor_descontos'], $dadosContrato['valor_final'], $dadosContrato['cidade'], $dadosContrato['ids_faturas_abertas']
    ]);

    if (!$query)
        throw new Exception('Erro ao salvar dados pre');

    return true;
}

function getIdProdutoInternet($id_contrato) {
    global $dbh_ixc;
}