<?php

function updateRow($row, $table, $id = null)
{
	if ($id === null)
		$id = $row['id'];

	$api = getIxcApi();

	foreach ($row as &$value) {
		if (gettype($value) === 'string')
			$value = utf8_encode($value);

		if(isDateYmd($value))
			$value = YmdToDmy($value);
	}

	if ($table == 'cliente_contrato') {
		if($row['avalista_1'] == 0 && $row['avalista_2'] == 0) {
			$row['avalista_1'] = '';
			$row['avalista_2'] = '';
		}
		if ($row['motivo_cancelamento'] == 0)
			$row['motivo_cancelamento'] = ' ';
	}

	$registro = $id; //registro a ser editado
	$api->put($table, $row, $registro);
	$resposta = $api->getRespostaConteudo(true); // false para json | true para array

	return $resposta;
}

function updateCliente($cliente) {
	$api = getIxcApi();

	foreach ($cliente as &$value) {
		if (gettype($value) === 'string')
			$value = utf8_encode($value);

		if(isDateYmd($value))
			$value = YmdToDmy($value);
	}

	$registro = $cliente['id']; //registro a ser editado
	$api->put('cliente', $cliente, $registro);
	$resposta = $api->getRespostaConteudo(true); // false para json | true para array

	return $resposta;
}

function updateContrato($contrato) {
	$api = getIxcApi();

	foreach ($contrato as &$value) {
		// if ($value = '0000-00-00')
		// 	$value = '';
		if (gettype($value) === 'string')
			$value = utf8_encode($value);

		if(isDateYmd($value))
			$value = YmdToDmy($value);
	}

	if($contrato['avalista_1'] == 0 && $contrato['avalista_2'] == 0) {
		$contrato['avalista_1'] = '';
		$contrato['avalista_2'] = '';
	}
	if ($contrato['motivo_cancelamento'] == 0)
		$contrato['motivo_cancelamento'] = ' ';

	$registro = $contrato['id']; //registro a ser editado
	$api->put('cliente_contrato', $contrato, $registro);
	$resposta = $api->getRespostaConteudo(true); // false para json | true para array

	return $resposta;
}

function updateRadusuario($radusuario, $desconectarLogin = false) {
	$api = getIxcApi();

	foreach ($radusuario as &$value)
		if (gettype($value) === 'string')
			$value = utf8_encode($value);

	if ($radusuario['ip'] && trim($radusuario['ip']) != '')
		$radusuario['fixar_ip'] = 'S';

	if($radusuario['pd_ipv6'] && trim($radusuario['pd_ipv6']) != '')
		$radusuario['fixar_ipv6'] = 'S';

	if($radusuario['framed_pd_ipv6'] && trim($radusuario['framed_pd_ipv6']) != '')
		$radusuario['framed_fixar_ipv6'] = 'S';

	$radusuario['interface_transmissao_fibra'] = $radusuario['interface_transmissao'];

	$registro = $radusuario['id']; //registro a ser editado
	$api->put('radusuarios', $radusuario, $registro);
	$resposta = $api->getRespostaConteudo(true); // false para json | true para array
	
	if ($desconectarLogin)
		desconectarLogin($radusuario['id']);

	return $resposta;
}

function getCidadeByRadusuario($radusuario, $onlyId = false) {
	$dbh_ixc = getConnectionIxc();

	if ($radusuario['endereco_padrao_cliente'] == 'S' || ($radusuario['endereco_padrao_cliente'] == 'N' && $radusuario['cidade'] == 0)) {
		$stmt = $dbh_ixc->prepare("SELECT ci.nome AS cidade, ci.id AS id_cidade FROM cidade ci
    INNER JOIN cliente cl ON cl.cidade = ci.id WHERE cl.id = ?;");
		$stmt->execute(array($radusuario['id_cliente']));
		$cliente = $stmt->fetch(PDO::FETCH_ASSOC);

		if (!$onlyId)
			$cidade = $cliente['cidade'];
		else
			$cidade = $cliente['id_cidade'];
			
	} else {

		if (!$onlyId) {
			$stmt = $dbh_ixc->prepare("SELECT ci.nome AS cidade FROM cidade ci
    		WHERE ci.id = ?;");
			$stmt->execute(array($radusuario['cidade']));
			$result = $stmt->fetch(PDO::FETCH_ASSOC);
			$cidade = $result['cidade'];
		}
		else {
			$cidade = $radusuario['cidade'];
		}

	}

	return $cidade;
}

function format_mac($mac, $format='linux') {
 	$mac = preg_replace("/[^a-fA-F0-9]/",'',$mac);
 
	$mac = (str_split($mac,2));
	if(!(count($mac) == 6))
		return false;
 
	if($format == 'linux' || $format == ':'){
		return $mac[0]. ":" . $mac[1] . ":" . $mac[2]. ":" . $mac[3] . ":" . $mac[4]. ":" . $mac[5];
	}elseif($format == 'windows' || $format == '-'){
		return $mac[0]. "-" . $mac[1] . "-" . $mac[2]. "-" . $mac[3] . "-" . $mac[4]. "-" . $mac[5];
	}elseif($format == 'cisco'){
		return $mac[0] . $mac[1] . ":" . $mac[2] . $mac[3] . ":" . $mac[4] . $mac[5];
	}else{
		return $mac[0]. "$format" . $mac[1] . "$format" . $mac[2]. "$format" . $mac[3] . "$format" . $mac[4]. "$format" . $mac[5];
	}
}

function getQuery($sql, $values) {
	foreach($values as $value) {
		$sql = replace_first_str('?', $value, $sql);
	}
	return $sql;
}

function replace_first_str($search_str, $replacement_str, $src_str){
  return (false !== ($pos = strpos($src_str, $search_str))) ? substr_replace($src_str, $replacement_str, $pos, strlen($search_str)) : $src_str;
}

/**
 * Replaces any parameter placeholders in a query with the value of that
 * parameter. Useful for debugging. Assumes anonymous parameters from 
 * $params are are in the same order as specified in $query
 *
 * @param string $query The sql query with parameter placeholders
 * @param array $params The array of substitution parameters
 * @return string The interpolated query
 */
function interpolateQuery($query, $params) {
    $keys = array();
    $values = $params;

    # build a regular expression for each parameter
    foreach ($params as $key => $value) {
        if (is_string($key)) {
            $keys[] = '/:'.$key.'/';
        } else {
            $keys[] = '/[?]/';
        }

        if (is_array($value))
            $values[$key] = implode(',', $value);

        if (is_null($value))
            $values[$key] = 'NULL';
    }
    // Walk the array to see if we can add single-quotes to strings
    array_walk($values, create_function('&$v, $k', 'if (!is_numeric($v) && $v!="NULL") $v = "\'".$v."\'";'));

    $query = preg_replace($keys, $values, $query, 1, $count);

    return $query;
}

function desconectarLogin($id) {

	$api = getIxcApi();

	$params = array(
		'id' => $id // ID LOGIN
	);
	
	$api->get('desconectar_clientes',$params);

	$resposta = $api->getRespostaConteudo(true);

	return $resposta;
}

function YmdToDmy($str) { 
	$timestamp = strtotime($str);
	$dmY = date("d/m/Y", $timestamp);
	return $dmY;
}

function isDate($str) {
	return preg_match("/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/",$str);
}

function isDateYmd($dataString) {
    // Tenta criar um objeto DateTime a partir da string de data e do formato especificado
    $data = DateTime::createFromFormat('Y-m-d', $dataString);
    
    // Verifica se ocorreu algum erro durante a criação do objeto DateTime
    // e se a data formatada corresponde à string original
    if ($data && $data->format('Y-m-d') === $dataString) {
        return true; // Formato válido
    } else {
        return false; // Formato inválido
    }
}

function getRadusuario($login)
{
	$dbh_ixc = getConnectionIxc();
	$sql = "SELECT rpr.descricao AS transmissor, r.* FROM radusuarios r
	LEFT JOIN radpop_radio rpr ON rpr.id = r.id_transmissor
	LEFT JOIN radpop rp ON rp.id = rpr.id_pop
	WHERE r.login = ? LIMIT 1";
	$stmt = $dbh_ixc->prepare($sql);
	$query = $stmt->execute(array($login));
	$radusuario = $stmt->fetch(PDO::FETCH_ASSOC);
	return $radusuario;
}

function getCliente($id)
{
	$dbh_ixc = getConnectionIxc();
	$sql = "SELECT * FROM cliente c
	WHERE c.id = ? LIMIT 1";
	$stmt = $dbh_ixc->prepare($sql);
	$query = $stmt->execute(array($id));
	$cliente = $stmt->fetch(PDO::FETCH_ASSOC);
	return $cliente;
}

function getContrato($id)
{
	$dbh_ixc = getConnectionIxc();
	$sql = "SELECT * FROM cliente_contrato cc
	WHERE cc.id = ? LIMIT 1";
	$stmt = $dbh_ixc->prepare($sql);
	$query = $stmt->execute(array($id));
	$contrato = $stmt->fetch(PDO::FETCH_ASSOC);
	return $contrato;
}


//Function to call which uses the PHP imap_*() functions to save messages
function save_mail($mail) {
	//You can change 'Sent Mail' to any other folder or tag
	//Use imap_getmailboxes($imapStream, '/imap/ssl') to retrieve a list of available folders or tags
	$path = "{mail.telemidia.net.br:143}sent";
	//Tell your server to open an IMAP connection using the same username and password as you used for SMTP
	$imapStream = imap_open($path, $mail->Username, $mail->Password);
	$result = imap_append($imapStream, $path, $mail->getSentMIMEMessage());
	imap_close($imapStream);
	return $result;
}

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;
function getPhpMailer($options) {
	$mail = new PHPMailer(true);
	//$mail->SMTPDebug = SMTP::DEBUG_SERVER;                    // Enable verbose debug output
	$mail->SMTPDebug = 0;
	$mail->isSMTP();
	$mail->Host       = 'mail.telemidia.net.br';
	$mail->SMTPAuth   = true;
	$mail->Username   = $options['username'];
	if (isset($options['reply_to'])) {
		$mail->ClearReplyTos();
		$mail->addReplyTo($options['reply_to']['address'], $options['reply_to']['name']);
	}
	$mail->setFrom($options['username'], $options['name']);
	$mail->Password   = $options['password'];
	$mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
	$mail->Port       = 587;
	$mail->SMTPOptions = array(
		'ssl' => array(
			'verify_peer' => false,
			'verify_peer_name' => false,
			'allow_self_signed' => true
		)
	);
	$mail->isHTML(true);
	$mail->CharSet = 'UTF-8';

	return $mail;
}

if (!function_exists('mb_ucfirst')) {
    function mb_ucfirst($string, $encoding)
    {
        $firstChar = mb_substr($string, 0, 1, $encoding);
        $then = mb_substr($string, 1, null, $encoding);
        return mb_strtoupper($firstChar, $encoding) . $then;
    }
}

function getCompanyInfo($cidade = null) {
	$companies_info = json_decode(file_get_contents(__DIR__ . '/companyInfo.json'), true);

	$COMPANY_INFO = NULL;
	foreach ($companies_info as $company => $info)
		if (mb_strtoupper(trim($cidade)) === mb_strtoupper(trim($company)))
			$COMPANY_INFO = $info;

	if (!$COMPANY_INFO)
		$COMPANY_INFO = $companies_info['matriz'];

	$COMPANY_INFO = array_merge($companies_info['matriz'], $COMPANY_INFO);

	$COMPANY_INFO['matriz'] = $companies_info['matriz'];

	return $COMPANY_INFO;
}

function getDivergenciasSkyInfo() {
	$divergenciasSkyInfo = json_decode(file_get_contents(__DIR__ . '/divergenciasSky.json'), true);
	

	return $divergenciasSkyInfo;
}

function getIdClienteByIdContrato($id_contrato) {
    $dbh_ixc = getConnectionIxc();

	$sql = "SELECT id_cliente
        FROM cliente_contrato cc
        WHERE cc.id = ?
        LIMIT 1;";

	$stmt = $dbh_ixc->prepare($sql);
	$query = $stmt->execute(array($id_contrato));
	$id_cliente = $stmt->fetch(PDO::FETCH_COLUMN);

	return $id_cliente;
}